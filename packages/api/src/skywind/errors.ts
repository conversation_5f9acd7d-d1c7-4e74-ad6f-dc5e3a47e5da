import { measures } from "@skywind-group/sw-utils";
import { ErrorResponse } from "@skywind-group/sw-management-i18n";
import { ExtraData } from "@skywind-group/sw-management-adapters";
import {
    ExtraMessageImpl,
    MrchExtraDataImpl,
    PopupButtonGameActions,
    PopupButtonImpl,
    SWError
} from "@skywind-group/sw-wallet-adapter-core";
import { encodeId } from "./utils/publicid";
import { DeploymentGroupType } from "./entities/deploymentGroup";
import { StaticDomainType } from "./entities/domain";

export enum ERROR_LEVEL {
    ERROR,
    WARN,
    INFO,
    DEBUG,
}

const capitalize = (name) => name.charAt(0).toUpperCase() + name.slice(1);

/**
 * Intended for cases when we need to use merchant's error message - ensure that no html injection can reach client
 * @param stringToEscape
 */
export function escapeSomeHtmlChars(stringToEscape: string): string {
    if (stringToEscape && typeof stringToEscape === "string") {
        return stringToEscape.replace("<", "&lt;")
            .replace(">", "&gt;")
            .replace("&", "&amp;")
            .replace("\"", "&quot;")
            .replace("'", "&#39;");
    }
    return stringToEscape;
}

export class InternalServerError extends SWError {
    constructor(error) {
        super(500, 1, "Internal server error " + JSON.stringify(error), ERROR_LEVEL.ERROR);
    }
}

export class ApiNotFoundError extends SWError {
    constructor() {
        super(404, 2, "API not found", ERROR_LEVEL.INFO);
    }
}

export class TokenIsMissing extends SWError {
    constructor() {
        super(401, 10, "Access Token is missing");
    }
}

export class TerminalTokenIsMissing extends SWError {
    constructor() {
        super(401, 11, "Terminal Access Token is missing");
    }
}

export class InvalidAccessToken extends SWError {
    constructor() {
        super(401, 12, "Invalid access token");
    }
}

export class ValidationError extends SWError {
    // data that shall be returned in the root of error response object (along with code and message fields)
    private decoratorData: ExtraData;

    constructor(messages: string | string[], public subErrors?: SWError[], decoratorData?: ExtraData) {
        super(400, 40, `Validation error: ${messages}`, ERROR_LEVEL.WARN);
        this.data.messages = Array.isArray(messages) ? messages.join(", ") : messages;
        this.decoratorData = decoratorData;
    }

    public decorateResponseWithData(errorResponse: ErrorResponse): ErrorResponse {
        if (this.decoratorData) {
            return { ...errorResponse, ...this.decoratorData };
        }

        return errorResponse;
    }
}

export class ValidationJackpotConfigurationError extends ValidationError {
    constructor(jackpotConfigurationLevel: number, allowedJackpotConfigurationLevel: number, jurisdictionCode: string) {
        super(
            `The jackpot configuration level (${jackpotConfigurationLevel}) ` +
            "cannot be greater than the jurisdiction's allowed configuration level " +
            `- ${jurisdictionCode} (${allowedJackpotConfigurationLevel})`
        );
        this.code = 45;
    }
}

export class ValidationJackpotConfigurationEntityMismatchError extends ValidationError {
    constructor(actualEntityId: number, expectedEntityId: number) {
        super(
            `The entity id (${actualEntityId}) is not the same (or doesn't have a parent) as the one set for ` +
            `the jackpot instance (${expectedEntityId})`
        );
        this.code = 46;
    }
}

export class ValidationJackpotConfigurationJurisdictionMismatchError extends ValidationError {
    constructor(actualJurisdictionCode: string, expectedJurisdictionCode: string) {
        super(
            `The jurisdiction code of the entity (${actualJurisdictionCode}) is not the same as the one set for ` +
            `the jackpot instance (${expectedJurisdictionCode})`
        );
        this.code = 47;
    }
}

export class ValidationEmailError extends SWError {
    constructor() {
        super(400, 41, "The Email Address is in an invalid format");
    }
}

export class ValidationPasswordError extends SWError {
    constructor() {
        super(400, 43, "Provided password is not valid");
    }
}

export class PromotionPlayersValidationError extends ValidationError {
    constructor(messages: string | string[], players: string[], promotions?: Map<string, number | number[]>) {
        super(messages, undefined, PromotionPlayersValidationError.buildErrorDetails(players, promotions));
        this.code = 44;
    }

    private static buildErrorDetails(players: string[], promotions?: Map<string, number | number[]>): any {
        const details: any = { players };
        if (promotions) {
            details.promotions = {};
            for (const [playerCode, promoId] of promotions.entries()) {
                const ids = Array.isArray(promoId) ? promoId : [promoId];
                details.promotions[playerCode] = ids.map(id => encodeId(id));
            }
        }
        return details;
    }
}

export class NotMasterEntityError extends SWError {
    constructor() {
        super(403, 50, "Not master entity");
    }
}

export class EntityCouldNotBeFound extends SWError {
    constructor() {
        super(404, 51, "Could not find entity");
    }
}

export class EntityAlreadyExistError extends SWError {
    constructor() {
        super(409, 60, "Entity already exists");
    }
}

export class ParentNotFoundError extends SWError {
    constructor() {
        super(400, 61, "Parent not found");
    }
}

export class ParentSuspendedError extends SWError {
    constructor() {
        super(400, 62, "One of the parents is suspended");
    }
}

export class OptimisticLockException extends SWError {
    constructor() {
        super(409, 63, "Entity is being edited now!");
    }
}

export class EntityNotEmpty extends SWError {
    constructor(tableName: string) {
        let message: string = "Entity is not empty.";
        if (tableName) {
            message += ` It has ${tableName} data`;
        }
        super(400, 64, message);
    }
}

export class EmailMissingError extends SWError {
    constructor() {
        super(400, 65, "User does not have an email configured");
    }
}

export class CountryNotFoundError extends SWError {
    constructor(country: string) {
        super(404, 80, `Country ${country} not found`);
        this.data.country = country;
    }
}

export class CannotRemoveCountryDefaultError extends SWError {
    constructor(code: string) {
        super(400, 81, `Country with code ${code} is default. You cannot remove default country`);
        this.data.code = code;
    }
}

export class CountryNotInListError extends SWError {
    constructor(country: string) {
        super(400, 82, `Country ${country} not in list`);
        this.data.country = country;
    }
}

export class CurrencyNotFoundError extends SWError {
    constructor(currency?: string) {
        super(404, 85, currency ? `Currency ${currency} not found` : "Currency not found");
        this.data.currency = currency || "";
    }
}

export class CannotRemoveCurrencyDefaultError extends SWError {
    constructor(currency: string) {
        super(400, 86, `Currency ${currency} is default. You cannot remove default currency`);
        this.data.currency = currency;
    }
}

export class CurrencyNotInListError extends SWError {
    constructor(currency: string) {
        super(400, 87, `Currency ${currency} not in list`);
        this.data.currency = currency;
    }
}

export class CurrencyNotExistInParentError extends SWError {
    constructor(currency: string) {
        super(400, 88, `Currency ${currency} not exist in parent`);
        this.data.currency = currency;
    }
}

export class CurrencyNotExistError extends SWError {
    constructor(currency: string) {
        super(400, 89, `Currency ${currency} not exist`);
        this.data.currency = currency;
    }
}

export class CurrencyNotSocialError extends SWError {
    constructor(currency: string) {
        super(400, 8084, `Currency ${currency} is not social`);
        this.data.currency = currency;
    }
}

export class SocialCurrencyNotAllowedError extends SWError {
    constructor(currency: string) {
        super(400, 8089, `Social currency ${currency} is not allowed in non social entity`);
        this.data.currency = currency;
    }
}

export class AmountIsNegativeError extends SWError {
    constructor() {
        super(400, 90, "Amount is negative");
    }
}

export class MaxCapacityReached extends SWError {
    constructor() {
        super(400, 108, "Max capacity of transaction is reached");
    }
}

export class LanguageNotFoundError extends SWError {
    constructor(language) {
        super(404, 93, `Language ${language} not found`);
        this.data.language = language;
    }
}

export class CannotRemoveLanguageDefaultError extends SWError {
    constructor(language) {
        super(400, 94, `Language ${language} is default. You cannot remove default language`);
        this.data.language = language;
    }
}

export class LanguageNotInListError extends SWError {
    constructor(language) {
        super(400, 95, `Language ${language} not in list`);
        this.data.language = language;
    }
}

export class LanguageNotExistInParentError extends SWError {
    constructor(language) {
        super(400, 96, `Language ${language} not exist in parent`);
        this.data.language = language;
    }
}

export class CountriesIsNotArray extends SWError {
    constructor() {
        super(400, 97, "Countries is not array");
    }
}

export class CurrenciesIsNotArray extends SWError {
    constructor() {
        super(400, 98, "Currencies is not array");
    }
}

export class LanguagesIsNotArray extends SWError {
    constructor() {
        super(400, 99, "Languages is not array");
    }
}

export class PlayerAlreadyExistError extends SWError {
    constructor(details) {
        super(409, 100, "Player" + details ? ` ${details}` : "" + " already exist");
        this.data.details = details;
    }
}

export class NotBrand extends SWError {
    constructor() {
        super(400, 101, "Not a brand");
    }
}

export class PlayerNotFoundError extends SWError {
    constructor() {
        super(404, 102, "Player not found");
    }
}

export class PlayerSessionNotFoundError extends SWError {
    constructor() {
        super(404, 105, "Session not found");
    }
}

export class PlayerSessionFinishedError extends SWError {
    constructor() {
        super(404, 106, "Session already finished");
    }
}

export class NotBrandOrMerchant extends SWError {
    constructor() {
        super(400, 109, "Operation is permitted only for brands or merchants");
    }
}

export class EntityHasDuplicates extends SWError {
    constructor(duplicatedEntity) {
        super(409, 112, `Entity subtree contains duplicate ${duplicatedEntity}`);
    }
}

export class PlayerInfoCannotBeSaved extends SWError {
    constructor() {
        super(400, 113, "Player personal information cannot be saved");
    }
}

export class BulkActionLimitError extends SWError {
    constructor(limit, receivedCount) {
        super(400, 151, `Too many items for group action, limit is ${limit} and received ${receivedCount}`);
        this.data.limit = limit;
        this.data.receivedCount = receivedCount;
    }
}

export class UpdateMultipleGameTypesError extends SWError {
    constructor() {
        super(400, 153, "Attempt to set the same limit filters for multiple game types");
    }
}

export class BulkActionDbError extends SWError {
    constructor() {
        super(400, 152, "Incorrect action query");
    }
}

export class SameTrxIdDifferentTransactionData extends SWError {
    constructor(message?: string) {
        super(400, 154, message || "Payment has difference between previous and current transfer data");
    }
}

export class LimitsIncorrect extends SWError {
    constructor(type, currency, invalidField: string = "") {
        super(400, 103, `Limits for game type ${type} and currency ${currency} incorrect.` +
            `Please check ${invalidField}`);
        this.data.type = type;
        this.data.currency = currency;
        this.data.invalidField = invalidField;
    }
}

export class LimitsForCurrencyNotFound extends SWError {
    constructor(currency) {
        super(404, 104, `Limits for currency ${currency} not found`);
        this.data.currency = currency;
    }
}

export class EmailAlreadyExistError extends SWError {
    constructor() {
        super(400, 199, "Email is already used");
    }
}

export class UserAlreadyExistError extends SWError {
    constructor() {
        super(409, 200, "User already exist");
    }
}

export class PasswordDoesntMatch extends SWError {
    constructor() {
        super(401, 201, "Password does not match");
    }
}

export class UserOrPasswordDoesntMatch extends SWError {
    constructor() {
        super(401, 202, "User or Password does not match");
    }
}

export class PasswordHasNotChanged extends SWError {
    constructor() {
        super(401, 224, "Password has not changed");
    }
}

export class PasswordNotUnique extends SWError {
    constructor() {
        super(400, 228, "New password should be different from a previous one");
    }
}

export class PlayerInfoHasNotChanged extends SWError {
    constructor() {
        super(400, 225, "Player info has not changed");
    }
}

export class UserNotExist extends SWError {
    constructor(additionalData?: any) {
        super(404, 198, "User does not exist");
        if (additionalData) {
            this.extraData = additionalData;
        }
    }
}

export class PasswordIncorrect extends SWError {
    constructor() {
        super(401, 203, "Password incorrect");
    }
}

export class PlayerCreatedWithoutPassword extends SWError {
    constructor() {
        super(401, 223, "Player created without password");
    }
}

export class PasswordAlreadyExists extends SWError {
    constructor() {
        super(401, 775, "Password already exists");
    }
}

export class AccessTokenError extends SWError {
    constructor() {
        super(401, 204, "Access token error");
    }
}

export class AccessTokenExpired extends SWError {
    constructor(reason: string = "") {
        super(401, 205, "Access Token is expired");
        this.data.reason = reason;
    }
}

export class InvalidRefreshToken extends SWError {
    constructor(reason: string = "") {
        super(401, 290, "Invalid Refresh Token");
        this.data.reason = reason;
    }
}

export class RefreshTokenExpired extends SWError {
    constructor(reason: string = "") {
        super(401, 291, "Refresh Token is expired");
        this.data.reason = reason;
    }
}

export class OAuthNotEnabledError extends SWError {
    constructor() {
        super(401, 879, "OAuth is not enabled");
    }
}

export class AccessSessionExpired extends SWError {
    constructor(userId: number, sessionId: string) {
        super(401, 792, `Access Session is expired for userId '${userId}' and sessionId '${sessionId}'`);
    }
}

export class TerminalTokenError extends SWError {
    constructor() {
        super(401, 208, "Terminal token error");
    }
}

export class OperationForbidden extends SWError {
    constructor(reason: string = "") {
        super(403, 206, reason ? `Forbidden. ${reason}` : "Forbidden");
        this.data.reason = reason;
    }
}

export class ProviderUserOrCodeOrSecretIsMissing extends SWError {
    constructor() {
        super(400, 207, "Provider user, code or secret is missing");
    }
}

export class ProviderSecretIncorrect extends SWError {
    constructor() {
        super(401, 209, "Provider secret incorrect");
    }
}

export class GameGroupAlreadyExists extends SWError {
    constructor(name) {
        super(409, 210, `Game group with name ${name} already exists`);
        this.data.name = name;
    }
}

export class GameGroupNotFound extends SWError {
    constructor() {
        super(404, 211, "Game group is not found");
    }
}

export class GameAlreadyExistsInGameGroup extends SWError {
    constructor(gameGroupName, gameCode) {
        super(409, 212, `Game ${gameCode} already exists in game group ${gameGroupName}`);
        this.data = { ...this.data, gameCode, gameGroupName };
    }
}

export class EntityGameNotFound extends SWError {
    constructor(gameCode) {
        super(404, 213, `Game ${gameCode} is not available for entity`);
        this.data.gameCode = gameCode;
    }
}

export class GameGroupNotEmpty extends SWError {
    constructor() {
        super(404, 234, "Game group is not empty");
    }
}

export class GameGroupIsDefault extends SWError {
    constructor() {
        super(409, 236, "Game group is used as default. Operation requires force flag");
    }
}

export class GameIsNotInGameGroup extends SWError {
    constructor() {
        super(400, 214, "Game doesn't belong to game group");
    }
}

export class AgentDomainNotExist extends SWError {
    constructor() {
        super(404, 215, "No record for this Domain in agents");
    }
}

export class AgentBadBrandId extends SWError {
    constructor() {
        super(500, 216, "Bad BrandId from list of agents or Domain is not unique");
    }
}

export class AgentChangeStatusFail extends SWError {
    constructor() {
        super(400, 217, "Bad query for updating agent status");
    }
}

export class AgentUpdateFail extends SWError {
    constructor() {
        super(400, 218, "Bad query for updating agent");
    }
}

export class ForbiddenToUpdatePromo extends SWError {
    constructor() {
        super(403, 219, "Can not update promo after being finished or archived");
    }
}

export class ForbiddenToRemovePromo extends SWError {
    constructor() {
        super(403, 237, "You can not remove an active package.");
    }
}

export class PaymentMethodBadBrandId extends SWError {
    constructor() {
        super(500, 220, "Bad BrandId or code is not unique");
    }
}

export class ResetPasswordLinkExpired extends SWError {
    constructor() {
        super(401, 221, "Reset password link is expired");
    }
}

export class ResetPasswordConfirmError extends SWError {
    constructor() {
        super(401, 222, "Reset password already complete or something go wrong");
    }
}

export class UserAuthenticationBlocked extends SWError {
    constructor(entityType: string) {
        const entityName = capitalize(entityType);
        const entityTypeIsUser = entityType === "user";
        const message = entityTypeIsUser ? "Login Failed, please contact Skywind Support to resolve the issue" :
                        `${entityName} authentication is blocked`;
        super(401, 230, message);
        this.data.entityName = entityName;
        if (entityTypeIsUser) {
            this.translateMessage = false;
        }
    }
}

export class ChangePasswordBlocked extends SWError {
    constructor(entityType: string) {
        const entityName = capitalize(entityType);
        super(401, 231, `${entityName} change password is blocked`);
        this.data.entityName = entityName;
    }
}

export class ResetPasswordBlocked extends SWError {
    constructor(entityType: string, ip: string) {
        const entityName = capitalize(entityType);
        super(401, 235, `${entityName} reset password from IP ${ip} is blocked`);
        this.data.entityName = entityName;
        this.data.ip = ip;
    }
}

export class ClearBlockedAuthenticationFails extends SWError {
    constructor(entityType: string) {
        const entityName = capitalize(entityType);
        super(400, 232, `${entityName} isn't locked`);
    }
}

export class ClearBlockedChangingPasswordFails extends SWError {
    constructor(entityType: string) {
        super(400, 233, `Changing password is available for ${entityType}`);
    }
}

export class SiteTokenChangeStatusFail extends SWError {
    constructor() {
        super(400, 226, "Bad query for updating token status");
    }
}

export class SiteTokenNotExist extends SWError {
    constructor() {
        super(404, 227, "Site token does not exist");
    }
}

export class RoleNotCreated extends SWError {
    constructor() {
        super(500, 621, "Role record not created");
    }
}

export class RoleUpdateFail extends SWError {
    constructor() {
        super(400, 622, "Bad query for updating Role");
    }
}

export class RoleNotExist extends SWError {
    constructor() {
        super(404, 623, "Role not exist");
    }
}

export class FailedToDeleteRoleNeedForce extends SWError {
    constructor() {
        super(409, 624, "Failed to delete role with linked users. Need force flag");
    }
}

export class RoleAddToUserError extends SWError {
    constructor(reason?: string) {
        super(400, 625, "Add Role to User failed" + (reason ? ": " + reason : ""));
        this.data.reason = reason || "";
    }
}

export class RoleManageFailed extends SWError {
    constructor() {
        super(403, 626, "You cannot manage role");
    }
}

export class PermissionNotExistInList extends SWError {
    constructor(permission: string) {
        super(400, 229, `The '${permission}' Permission not exist in list`);
        this.data.permission = permission;
    }
}

export class GameNotFoundError extends SWError {
    constructor(gameCode: string) {
        super(404, 240, `Game not found. GameCode: ${gameCode}`);
        this.data.gameCode = gameCode;
    }
}

export class RtpDeductionGameNotFoundError extends SWError {
    constructor(gameCode: string, newRtpDeduction: number) {
        super(404, 242, `Game not found. GameCode: ${gameCode}. newRtpDeduction: ${newRtpDeduction}`);
    }
}

export class DeploymentGroupTypeError extends SWError {
    constructor(type: DeploymentGroupType) {
        super(422,
            255,
            `You can't set to ${type === DeploymentGroupType.GAME ?
                                "entity" :
                                "game"} deployment group with type ${type}`);
    }
}

export class GameAlreadyExistsError extends SWError {
    constructor() {
        super(409, 241, "Game already exists");
    }
}

export class FailedToDeleteEntityGameNeedForce extends SWError {
    constructor() {
        super(409, 302, "Failed to delete entity game with child entity games. Need force flag");
    }
}

export class FailedToDeleteEntityGame extends SWError {
    constructor() {
        super(400, 303, "Failed to delete entity game with child entity games");
    }
}

export class GameCategoryNotFound extends SWError {
    constructor() {
        super(404, 304, "Game category is not found");
    }
}

export class GameCategoryAlreadyExists extends SWError {
    constructor() {
        super(409, 305, "Game category already exists");
    }
}

export class GameSuspendedError extends SWError {
    constructor(gameCode) {
        super(400, 306, `Game ${gameCode} is suspended`);
        this.data.gameCode = gameCode;
    }
}

export class GameProviderAlreadyExistsError extends SWError {
    constructor() {
        super(409, 310, "Game provider already exists");
    }
}

export class GameProviderSuspendedError extends SWError {
    constructor(providerCode) {
        super(400, 311, `Game provider ${providerCode} is suspended`);
        this.data.providerCode = providerCode;
    }
}

export class GameProviderNotFoundError extends SWError {
    constructor() {
        super(404, 312, "Game provider not found");
    }
}

export class StartGameTokenError extends SWError {
    constructor() {
        super(400, 320, "Start game token error");
    }
}

export class StartGameTokenExpired extends SWError {
    constructor() {
        super(400, 321, "Start game token is expired", ERROR_LEVEL.INFO);
    }
}

export class TokensVerifyError extends SWError {
    constructor(message: string) {
        super(400, 345, `Start and Game Token verification error: ${message}`);
    }
}

export class GameTokenError extends SWError {
    constructor() {
        super(400, 322, "Game token error");
    }
}

export class GameTokenExpired extends SWError {
    constructor() {
        super(400, 323, "Game token expired", ERROR_LEVEL.INFO);
    }
}

export class LobbyNotFound extends SWError {
    constructor() {
        super(404, 324, "Lobby is not found");
    }
}

export class LobbyAlreadyExists extends SWError {
    constructor() {
        super(409, 325, "Lobby already exists");
    }
}

export class LobbyUpdateFail extends SWError {
    constructor() {
        super(400, 330, "Bad request for lobby updating");
    }
}

export class LobbyAssignedTerminals extends SWError {
    constructor(terminals: string[]) {
        super(400, 331, "Lobby could not be deleted cos it have assigned terminals: " + terminals.join(", "),
            ERROR_LEVEL.WARN);
        this.data.terminals = terminals;
    }
}

export class TerminalAlreadyExists extends SWError {
    constructor() {
        super(409, 326, "Terminal already exists");
    }
}

export class TerminalNotFound extends SWError {
    constructor() {
        super(404, 327, "Terminal is not found");
    }
}

export class TerminalSessionNotFound extends SWError {
    constructor() {
        super(404, 344, "Terminal Session is not found");
    }
}

export class TerminalUpdateFail extends SWError {
    constructor() {
        super(400, 328, "Bad request for terminal updating");
    }
}

export class TerminalQueryFail extends SWError {
    constructor() {
        super(400, 329, "Bad query for getting terminals");
    }
}

export class TerminalPlayerAlreadyLoggedIn extends SWError {
    constructor(playerCode) {
        super(400, 333, `Player ${playerCode} already has an active session in another Terminal`);
        this.data.playerCode = playerCode;
    }
}

export class NoReceiversForNotification extends SWError {
    constructor() {
        super(400, 340, "No receivers found for notification");
    }
}

export class NotificationNotFound extends SWError {
    constructor() {
        super(400, 341, "Notification not found");
    }
}

export class SiteAlreadyExists extends SWError {
    constructor() {
        super(409, 342, "Site already exists");
    }
}

export class SiteNotFound extends SWError {
    constructor() {
        super(404, 343, "Site not found");
    }
}

export class SortByInvalidError extends SWError {
    constructor(key) {
        const capitalizedKey = capitalize(key);
        super(400, 403, `${capitalizedKey} is not valid for sort by`);
        this.data.capitalizedKey = capitalizedKey;
    }
}

export class MerchantAlreadyExistError extends SWError {
    constructor() {
        super(409, 500, "Merchant already exists");
    }
}

export class MerchantTypeNotSupportedError extends SWError {
    constructor(type) {
        super(400, 501, `Merchant type ${type} is not supported`);
        this.data.type = type;
    }
}

export class MerchantNotFoundError extends SWError {
    constructor() {
        super(404, 502, "Merchant not found");
    }
}

export class MerchantDoesntSupportError extends SWError {
    constructor() {
        super(400, 503, "Merchant brand doesn't support this operation");
    }
}

export class NotMerchantError extends SWError {
    constructor() {
        super(400, 504, "Not a merchant brand");
    }
}

export class MerchantMisconfiguration extends SWError {
    constructor() {
        super(500, 505, "Merchant should have url and password in params");
    }
}

export class MerchantInternalError extends SWError {
    constructor(reason?: string) {
        super(500, 506, "Merchant internal error" + (reason ? `: ${escapeSomeHtmlChars(reason)}` : ""));
        this.data.reason = reason || "";
    }
}

export class MerchantFreeBetError extends SWError {
    constructor(reason?: string) {
        super(500, 508, "Merchant free bet error" + (reason ? `: ${reason}` : ""));
        this.data.reason = reason || "";
    }
}

export class MerchantCodeNotFound extends SWError {
    constructor(merchantCode) {
        super(404, 509, `Merchant code ${merchantCode} not found`);
    }
}

export class MissingCurrencyRatesError extends SWError {
    constructor() {
        super(500, 405, "Missing currency exchange rates");
    }
}

export class TransactionNotFound extends SWError {
    constructor() {
        super(404, 600, "Transaction not found");
    }
}

export class WhitelistNotFound extends SWError {
    constructor() {
        super(404, 110, "Whitelist not found");
    }
}

export class WhitelistAlreadyExists extends SWError {
    constructor() {
        super(409, 111, "Whitelist already exists. You can patch it");
    }
}

export class PaymentNotFoundError extends SWError {
    constructor() {
        super(404, 667, "Payment not found");
    }
}

export class PaymentMethodNotFoundError extends SWError {
    constructor() {
        super(400, 670, "Payment method not found");
    }
}

export class PaymentMethodTypeNotFoundError extends SWError {
    constructor() {
        super(400, 671, "Payment type should have \"deposit\" or \"withdrawal\" value");
    }
}

export class LabelNotCreate extends SWError {
    constructor() {
        super(400, 669, "Label record not created");
    }
}

export class LabelGroupNotCreate extends SWError {
    constructor() {
        super(400, 672, "Label group record not created");
    }
}

export class PaymentAPIError extends SWError {
    constructor(statusCode?: number, err?: SWError) {
        const status = statusCode || 400;
        const code = err && err.code || 300;
        const message = err && err.message || "Payment API respond with error";
        super(status, code, message);
    }
}

export class PaymentAPITransientError extends SWError {
    constructor(statusCode?: number, err?: SWError) {
        const status = statusCode || 500;
        const code = err && err.code || 301;
        const message = err && err.message || "Payment API transient error";
        super(status, code, message);
    }
}

export class MalformedJsonError extends SWError {
    constructor(reason: string) {
        super(400, 674, `Malformed JSON : ${reason}`);
        this.data.reason = reason || "";
    }
}

export class PromotionNotFoundError extends SWError {
    constructor() {
        super(404, 680, "Promotion not found");
    }
}

export class FailedToDeleteItemNeedForce extends SWError {
    constructor() {
        super(409, 682, "Failed to delete item that is referenced from other table. Need force flag");
    }
}

export class GameHistoryDetailsNotFound extends SWError {
    constructor() {
        super(404, 683, "Game history details not found");
    }
}

export class ForeignKeyReferencedItemNotFound extends SWError {
    constructor(item: string = "item") {
        super(404, 684, `Referenced ${item} is not found`);
        this.data.item = item;
    }
}

export class ConcurrentPromoTransactionError extends SWError {
    constructor() {
        super(400, 687, "Concurrent promotion transaction operation");
    }
}

export class PromoAlreadyAddedToPlayer extends SWError {
    constructor() {
        super(400, 688, "Promo already has been added to player");
    }
}

export class GameHistoryUrlNotFoundError extends SWError {
    constructor() {
        super(404, 689, "Game history URL not found");
    }
}

export class BonusCoinsNotAvailableError extends SWError {
    constructor() {
        super(400, 690, "Bonus coins not available");
    }
}

export class InsufficientBonusCoinsBrandBalance extends SWError {
    constructor() {
        super(400, 692, "Insufficient bonus coins brand balance");
    }
}

export class IpLocationError extends SWError {
    constructor(private causeError) {
        super(500, 700, "Ip location lookup error: " + causeError.message);
        this.data.message = causeError.message;
    }

    public cause() {
        return this.causeError;
    }
}

export class CountryIsRestricted extends SWError {
    constructor(country: string, source: string, reason = "") {
        super(403, 701, `Country ${country} [from ${source}] is restricted [${reason}]`);
        this.setExtraData({
            traceId: measures.measureProvider.getTraceID()
        });
        this.data = { country, source, reason };
    }
}

export class CurrencyIsRestricted extends SWError {
    constructor(currency: string, countryCode: string, source: string) {
        super(403, 702, `Currency ${currency} in country ${countryCode} [from ${source}] restricted`);
        this.setExtraData({
            traceId: measures.measureProvider.getTraceID()
        });
        this.data = { currency, countryCode, source };
    }
}

export class UnknownIpAddress extends SWError {
    constructor(ip: string) {
        super(400, 703, `IP address ${ip} cannot be resolved`);
        this.data.ip = ip;
    }
}

export class ExternalGameUrlError extends SWError {
    constructor() {
        super(400, 705, "Fail to obtain external game url");
    }
}

export class ForbiddenToUpdateNonPendingPromo extends SWError {
    constructor() {
        super(403, 706, "Can not update promo that is not pending");
    }
}

export class NumberOfTestPlayersIsExceeded extends SWError {
    constructor(maxNumber: number) {
        super(403, 707, `Brand has exceeded ${maxNumber} of test players. It's max number.`);
        this.data.maxNumber = maxNumber.toString();
    }
}

export class PlayerHasNoSuchPromoError extends SWError {
    constructor() {
        super(400, 709, "Player has no such promo");
    }
}

export class PromoIsNotInValidState extends SWError {
    constructor() {
        super(400, 711, "Promo is not in a valid state");
    }
}

export class PromoIsInvalidRewardIsMissing extends SWError {
    constructor() {
        super(400, 710, "Promo is not in a valid state. Reward is missing");
    }
}

export class UnauthorizedSite extends SWError {
    constructor(referer?: string) {
        super(403, 708, "It is forbidden to start game from unauthorized site: " + referer);
    }
}

export class PlayerIsSuspended extends SWError {
    constructor(level: number = ERROR_LEVEL.WARN, extraData?: ExtraData) {
        super(400, 712, "Player is suspended", level, extraData);
    }

    public static buildWithDefaultExtraData(): PlayerIsSuspended {
        const extraData = MrchExtraDataImpl.create().addExtraMessage(ExtraMessageImpl.create().setButtons([
            PopupButtonImpl.create()
                .setLabel("OK")
                .setGameAction(PopupButtonGameActions.lobby)
                .setTranslate(true)
        ]));
        return new PlayerIsSuspended(ERROR_LEVEL.WARN, extraData);
    }
}

export class PasswordNotConfirm extends SWError {
    constructor() {
        super(400, 713, "Password not confirm");
    }
}

export class SamePasswordAndUsernameError extends SWError {
    constructor() {
        super(400, 714, "Password should be different from username");
    }
}

export class PlayerIsOnline extends SWError {
    constructor() {
        super(400, 800, "Player is online");
    }
}

export class DomainNotFoundError extends SWError {
    constructor(id?: number) {
        super(404, 900, `Domain does not exist${id ? ": " + id : ""}`);
    }
}

export class DomainInUseError extends SWError {
    constructor() {
        super(409, 901, "Domain is used by entity");
    }
}

export class StaticDomainNotDefined extends SWError {
    constructor(type = StaticDomainType.STATIC) {
        super(400, 902, `Static domain of type ${type} is not defined`);
    }
}

export class DynamicDomainNotDefined extends SWError {
    constructor() {
        super(400, 903, "Dynamic domain is not defined");
    }
}

export class DynamicDomainAlreadyExists extends SWError {
    constructor() {
        super(409, 904, "Dynamic domain already exists");
    }
}

export class ExternalGameIdNotDefined extends SWError {
    constructor() {
        super(400, 909, "External game id is not defined");
    }
}

export class SiteCodeNotDefined extends SWError {
    constructor() {
        super(400, 910, "Site code is not defined");
    }
}

export class ProviderGameCodeNotDefined extends SWError {
    constructor() {
        super(400, 911, "Provider game code is not defined");
    }
}

export class DomainPoolNotFoundError extends SWError {
    constructor() {
        super(404, 912, "Domain pool not found");
    }
}

export class DomainPoolAlreadyExistsError extends SWError {
    constructor() {
        super(409, 913, "Domain pool already exists");
    }
}

export class DomainPoolItemNotFoundError extends SWError {
    constructor() {
        super(404, 914, "Domain pool item does not exist");
    }
}

export class DomainPoolItemAlreadyExistsError extends SWError {
    constructor() {
        super(409, 915, "Domain pool already exists");
    }
}

export class EntityDomainPoolNotDefinedError extends SWError {
    constructor() {
        super(404, 916, "Entity domain pool ID is not defined");
    }
}

export class TwoFATypeNotSetError extends SWError {
    public twoFATypes: string[];
    public twoFAToken: string;
    public userHasPhoneNumber: boolean;

    constructor(twoFATypes: string[], twoFAToken: string, userHasPhoneNumber: boolean) {
        super(401, 715, "Two Factor Authentication is not set for user");
        this.twoFATypes = twoFATypes;
        this.twoFAToken = twoFAToken;
        this.userHasPhoneNumber = userHasPhoneNumber;
    }

    // TODO: add decorateResponseWithExtraData and move code from login api controller
}

export class AuthCodeIsIncorrect extends SWError {
    constructor() {
        super(400, 699, "Provided auth code is incorrect");
    }
}

export class TwoFATokenExpired extends SWError {
    constructor() {
        super(401, 704, "Two factor auth token is expired");
    }
}

export class TwoFATokenError extends SWError {
    constructor() {
        super(401, 718, "Two factor auth token error");
    }
}

export class SmsSendingError extends SWError {
    constructor() {
        super(500, 719, "An error occurred when sending sms. Check logs");
    }
}

export class EmailSendingError extends SWError {
    constructor() {
        super(500, 720, "An error occurred when sending email. Check logs");
    }
}

export class AuthTypeIsNotAllowed extends SWError {
    constructor(authType) {
        super(400, 721, `Selected ${authType} auth type is not allowed`);
        this.data.authType = authType;
    }
}

export class TwoFANotConfiguredForEntity extends SWError {
    constructor() {
        super(400, 722, "Two factor auth is not configured for entity");
    }
}

export class TwoFAAuthCodeExpired extends SWError {
    constructor() {
        super(400, 723, "Two factor auth code was not generated or expired");
    }
}

export class ReportAlreadyExists extends SWError {
    constructor() {
        super(409, 724, "Report already exists. You can patch it");
    }
}

export class CreateReportError extends SWError {
    constructor() {
        super(400, 725, "Create Report request error");
    }
}

export class ReportSuspended extends SWError {
    constructor() {
        super(400, 728, "Report is suspended");
    }
}

export class ReportNotFound extends SWError {
    constructor() {
        super(400, 727, "Report not found");
    }
}

export class GetReportError extends SWError {
    constructor() {
        super(400, 726, "Reports list does not initialized");
    }
}

export class GetBiTicketError extends SWError {
    constructor(msg?: string) {
        super(400, 754, "Can not get trust ticket from Business Intelligence server " + msg);
    }
}

export class TwoFAAuthCodeHasBeenSentRecently extends SWError {
    constructor() {
        super(409, 753, "Two factor auth code has been sent recently. Repeat attempt a little later");
    }
}

export class EntityEnvIdChangedError extends SWError {
    constructor() {
        super(400, 730, "Entity has different environment id");
    }
}

export class JPNBadRequestError extends SWError {
    constructor(statusCode?: number, err?: SWError) {
        const status = statusCode || 400;
        const code = err && err.code || 716;
        const message = err && err.message || "JPN bad request error";
        super(status, code, message);
    }
}

export class JPNInternalServerError extends SWError {
    constructor(statusCode?: number, err?: SWError) {
        const status = statusCode || 500;
        const code = err && err.code || 717;
        const message = err && err.message || "JPN internal server error";
        super(status, code, message);
    }
}

export class ParentIsBrand extends SWError {
    constructor() {
        super(400, 731, "Parent is brand");
    }
}

export class MerchantPromoNotEnabledError extends SWError {
    constructor() {
        super(400, 732, "Promos are not enabled for merchant");
    }
}

export class PlayerBonusesNotEnabledError extends SWError {
    constructor() {
        super(400, 744, "Bonuses are not enabled for this entity");
    }
}

export class PlayerBonusNotFoundError extends SWError {
    constructor() {
        super(400, 745, "Bonus not found");
    }
}

export class EntityStatusNotMatchError extends SWError {
    constructor(status: string) {
        super(400, 734, "Entity status not match: " + status);
    }
}

export class EntityUnderMaintenanceError extends SWError {
    constructor() {
        super(400, 735, "Entity is under maintenance");
    }
}

export class MaintenanceUrlNotDefinedError extends SWError {
    constructor() {
        super(400, 736, "Entity is under maintenance, but maintenance url is not defined");
    }
}

export class EntityHasConflictsWithParent extends SWError {
    constructor(conflicts: any) {
        super(400, 737, "Child has conflicts with new parent");
        this.data.conflicts = conflicts;
    }
}

export class ParentGameIsNull extends SWError {
    constructor() {
        super(400, 738, "Parent game is null");
    }
}

export class ChangePasswordError extends SWError {
    public changePasswordToken: string;

    constructor(changePasswordToken: string) {
        super(401, 739, "User's password was not changed");
        this.changePasswordToken = changePasswordToken;
    }
}

export class DomainForbiddenError extends SWError {
    constructor(domain: string) {
        super(403, 740, "Domain is forbidden: " + domain);
    }
}

export class GameServerSettingsNotFound extends SWError {
    constructor() {
        super(404, 741, "Game server settings not found");
    }
}

export class GameServerSettingsExists extends SWError {
    constructor() {
        super(409, 742, "Game server settings already exists");
    }
}

export class GamesNotFound extends SWError {
    constructor() {
        super(404, 743, "Games not available");
    }
}

export class ResponsibleGamingIsNotEnabledError extends SWError {
    constructor() {
        super(403, 746, "Can't execute operation. Responsible gaming is not enabled.");
    }
}

export class ResponsibleGamingNotFound extends SWError {
    constructor(message?: string) {
        super(404, 747, message || "Responsible gaming settings not found");
    }
}

export class PlayerCodeIsTaken extends SWError {
    constructor() {
        super(409, 750, "Player code is in use");
    }
}

export class ReferrerMissingError extends SWError {
    constructor() {
        super(403, 751, "Referrer is missing");
    }
}

export class ExceedBetLimit extends SWError {
    constructor() {
        super(403, 752, "Bet limit was exceeded");
    }
}

export class CurrencyMismatch extends SWError {
    constructor(message?: string) {
        super(400, 755, message || "Player default currency doesn't match with requested");
    }
}

export class DuplicateTransactionError extends SWError {
    constructor(trxId: string) {
        super(400, 756, `Duplicate transaction: ${trxId}`);
    }
}

export class MerchantDuplicateTransactionError extends SWError {
    constructor() {
        // 500 error code as we don't want our game server to do a rollback on such response
        super(500, 757, "Merchant has already received transaction request");
    }
}

export class ForbidToRecoverGameWithJPError extends SWError {
    constructor() {
        super(500, 759, "Forbid to recover game with jackpot");
    }
}

export class ErrorQueryingGameServer extends SWError {
    constructor(gameServerSWError?: SWError) {
        const message = gameServerSWError ?
                        `An error on gameserver side : ${gameServerSWError.code}, ${gameServerSWError.message}`
                                          : "An error occurred while querying gameserver";
        super(400, 760, message);
    }
}

export class GameServerRequestError extends SWError {
    // TODO: in 3.7 refactor with decorate response or subErrors field as in ValidationError
    constructor(gameServerSWError?: SWError) {
        const message = gameServerSWError ?
                        `Game Server request error : ${gameServerSWError.code}, ${gameServerSWError.message}`
                                          : "An error occurred querying game server";
        super(400, 761, message);
    }
}

export class EntityInfoRecordAlreadyExists extends SWError {
    constructor() {
        super(409, 763, "Additional info for an entity already stored");
    }
}

export class EntityInfoRecordNotFound extends SWError {
    constructor() {
        super(404, 764, "Additional info for an entity not found");
    }
}

export class EntityInfoUpdateFail extends SWError {
    constructor() {
        super(400, 765, "Bad query for updating additional info of entity");
    }
}

export class HistoryServerConnectionError extends SWError {
    constructor() {
        super(500, 767, "Can't connect to History Server.");
    }
}

export class HistoryServerError extends SWError {
    constructor(code?: number, body?: string) {
        super(code || 500, 768, body || "History Server internal error.");
    }
}

export class GameGroupLimitNotFound extends SWError {
    constructor() {
        super(404, 769, "Game group limit not found");
    }
}

export class CannotMarkMigrationFinishedError extends SWError {
    constructor() {
        super(404, 770, "Cannot mark migration finished");
    }
}

export class MigrationIsInProgressError extends SWError {
    constructor() {
        super(404, 771, "Migration is in progress for one of the entity");
    }
}

export class RequireMigrationError extends SWError {
    constructor(public readonly entityId: number, public readonly newEnvironment: string) {
        super(404, 773, "Changing dynamic domain requires migration");
    }
}

export class LiveManagerError extends SWError {
    constructor(reason?: SWError) {
        const message = reason ?
                        `An error on live manager side : ${reason.code}, ${reason.message}`
                               : "An error occurred while querying live manager";
        super(500, 774, message);
    }
}

export class CannotDeleteIpFromParent extends SWError {
    constructor() {
        super(401, 776, "Cannot delete IP from parent entity");
    }
}

export class SplitPaymentNotSupportedError extends SWError {
    constructor(what: string) {
        super(404, 777, `Split payment operation not supported for ${what}`);
    }
}

export class MerchantTypeIsNotSupportedByParentError extends SWError {
    constructor(type: string | string[]) {
        super(400, 780, `Merchant types [${type}] are not supported by parent`);
        this.data.type = type;
    }
}

export class MerchantTypesIsNotArray extends SWError {
    constructor() {
        super(400, 781, "Merchant types is not array");
    }
}

export class MerchantTypeIsNotAvailable extends SWError {
    constructor(merchantType: string | string[]) {
        super(400, 782, `Merchant types [${merchantType}] are inherited from parent and can not be removed.`);
    }
}

export class MerchantTypeNotFound extends SWError {
    constructor(merchantType) {
        super(404, 783, `Merchant type ${merchantType} not found`);
    }
}

export class MerchantTypeExists extends SWError {
    constructor(merchantType: string | string[]) {
        super(409, 784, `Merchant types [${merchantType}] already exist`);
    }
}

export class InternalTokenIsMissing extends SWError {
    constructor() {
        super(401, 785, "Internal Token is missing");
    }
}

export class InternalTokenError extends SWError {
    constructor() {
        super(401, 786, "Internal Token error");
    }
}

export class InternalTokenExpired extends SWError {
    constructor() {
        super(401, 787, "Internal Token is expired");
    }
}

export class SchemaDefinitionAlreadyExists extends SWError {
    constructor(name) {
        super(409, 788, `Schema definition with name ${name} already exists`);
        this.data.name = name;
    }
}

export class SchemaConfigurationAlreadyExists extends SWError {
    constructor(definitionName: string, entityId: number) {
        super(409, 789, `Schema configuration for definition ${definitionName} for entity ${entityId}` +
            " already exists");
    }
}

export class GameLimitsConfigurationAlreadyExists extends SWError {
    constructor() {
        super(409, 790, "Game limits configuration already exists");
    }
}

export class DynamicDomainTagsNotSupportedError extends SWError {
    constructor() {
        super(400, 791, "Dynamic domain tags not supported");
    }
}

export function isSWError(err) {
    return err.responseStatus && err.code && err.message;
}

export class ResourceNotFoundError extends SWError {
    constructor(massage) {
        super(404, 793, massage);
    }
}

export class InvalidConfigurationStatus extends SWError {
    constructor() {
        super(400, 794, "Invalid Configuration Status");
    }
}

export class InvalidUserType extends SWError {
    constructor() {
        super(400, 795, "Invalid User Type");
    }
}

export class PhantomError extends SWError {
    constructor(error?: SWError, status?: number) {
        super(status || 400, error?.code || 796, `Phantom Error ${error?.message}`, ERROR_LEVEL.ERROR);
    }
}

export class CurrencyMultiplierAlreadyExists extends SWError {
    constructor() {
        super(409, 797, "Currency multiplier already exists");
    }
}

export class EntityGamesNotFoundError extends SWError {
    constructor(gameCodes: string[]) {
        super(404, 798, `Game codes ${gameCodes} not found or are not available for entity`);
    }
}

export class RangeIntersectError extends SWError {
    constructor() {
        super(400, 801, "roundId or sessionId range was intersected");
    }
}

export class GameLaunchForbidden extends SWError {
    constructor() {
        super(403, 802, "This game can only be played in real money mode");
    }
}

export class GameLauncherTokenExpired extends SWError {
    constructor() {
        super(400, 803, "Game launcher token is expired", ERROR_LEVEL.INFO);
    }
}

export class GameLauncherTokenError extends SWError {
    constructor() {
        super(400, 804, "Game launcher token error");
    }
}

export class PlayerTestModeError extends SWError {
    constructor(reason?: string) {
        super(400, 805, "Player should be in test mode" + (reason ? `: ${reason}` : ""));
    }
}

export class DeploymentGroupNotFound extends SWError {
    constructor() {
        super(400, 908, "Deployment group not found");
    }
}

export class PlayerCodeIsInvalidError extends SWError {
    constructor(playerCode: string, foundSymbols: string) {
        super(400, 808, `Player code is invalid. Symbols ['${foundSymbols}'] are found in player code '${playerCode}'.`
            +
            " Can't use symbols of ':', tab, new line in player code.");
    }
}

export class InvalidEGPPromoConfiguration extends SWError {
    constructor(message = "Invalid configuration") {
        super(500, 809, message);
    }
}

export class GameVersionsUpdateError extends SWError {
    constructor() {
        super(400, 811, "Failed to update game versions");
    }
}

export class LimitTemplateAlreadyExistError extends SWError {
    constructor(name: string) {
        super(400, 812, `Limit template ${name} already exists`);
    }
}

export class CaptchaDoesntMatch extends SWError {
    constructor(additionalData?: any) {
        super(401, 813, "Captcha does not match");
        if (additionalData) {
            this.extraData = additionalData;
        }
    }
}

export class CaptchaParamsDontExist extends SWError {
    constructor(additionalData?: any) {
        super(401, 814, "Captcha params do not exist");
        if (additionalData) {
            this.extraData = additionalData;
        }
    }
}

export class CaptchaParamsNotValid extends SWError {
    constructor(additionalData?: any) {
        super(401, 815, "Captcha params are not valid");
        if (additionalData) {
            this.extraData = additionalData;
        }
    }
}

export class PlayerOrPasswordDoesntMatch extends SWError {
    constructor(additionalData?: any) {
        super(401, 816, "Player or Password does not match");
        if (additionalData) {
            this.extraData = additionalData;
        }
    }
}

/**
 * Hidden merchant is used for Integrations
 */
export class HiddenMerchantNotFound extends SWError {
    constructor(additionalData?: any) {
        super(400, 817, "Hidden merchant not found. Wrong integration configuration or wrong operator api user.");
        if (additionalData) {
            this.extraData = additionalData;
        }
    }
}

export class MerchantPlayerGameGroupEntitySettingIsInactive extends SWError {
    constructor(additionalData?: any) {
        super(400, 818,
            "Entity setting name of merchantPlayerGameGroup is not set to true. You can't use appropriate API");
        if (additionalData) {
            this.extraData = additionalData;
        }
    }
}

export class UniqueConstraintError extends SWError {
    constructor(code: number = 819, fieldsData?: object, modelName?: string) {
        let message = `${modelName} record not created. Unique constraint error`;
        if (fieldsData) {
            const values: any[][] = Object.entries(fieldsData);
            const fields: string[] = values.map(v => v.join(": "));
            message += ` for values: {${fields.join(", ")}}`;
        }
        super(400, code, message);
        this.extraData = fieldsData;
    }
}

export class AuditSummaryGetOrCreateError extends SWError {
    constructor() {
        super(404, 820, "AuditSummary can't be found or created");
    }
}

export class AuditSummaryCreationError extends SWError {
    constructor() {
        super(400, 821, "AuditSummary record not created");
    }
}

export class AuditSummaryUniqueConstraintError extends UniqueConstraintError {
    constructor(fieldsData?: any) {
        super(822, fieldsData, "AuditSummary");
    }
}

export class AuditSummaryValidationError extends ValidationError {
    constructor(errs?: { message: string }[]) {
        let messages: string[] = [];
        if (errs) {
            messages = errs.map(e => e.message);
        }
        super(messages);
        this.code = 823;
    }
}

export class TokenAlreadyBlocked extends SWError {
    constructor(type: string) {
        super(401, 824, `${type} token is already blocked`);
    }
}

export class TokenBlocked extends SWError {
    constructor(type: string) {
        super(401, 825, `${type} token is blocked`);
    }
}

export class AuditSessionCreationError extends SWError {
    constructor() {
        super(400, 826, "AuditSession record not created");
    }
}

export class AuditSessionNotFoundError extends SWError {
    constructor() {
        super(404, 827, "AuditSession is not found");
    }
}

export class AuditSessionUpdateError extends SWError {
    constructor() {
        super(400, 828, "AuditSession update error");
    }
}

export class AuditLoginError extends SWError {
    constructor() {
        super(400, 829, "Fail to audit login");
    }
}

export class AuditSummaryGetError extends SWError {
    constructor() {
        super(404, 1830, "AuditSummary can't be found");
    }
}

export class GamesRtpDeductionInvalidRequest extends SWError {
    constructor(messageListErrors: string) {
        super(400, 836, messageListErrors);
    }
}

export class AnotherGameInProgress extends SWError {
    constructor(model: string) {
        super(400, 835, "Another game is currently in progress");
    }
}

export class NicknameAlreadyExistError extends SWError {
    constructor() {
        super(400, 850, "Nickname is already used");
    }
}

export class NicknameIdenticalUsernameError extends SWError {
    constructor(nickName: string) {
        super(400, 851, `${nickName} Nickname must be different from your login name.`);
    }
}

export class NicknameSymbolsError extends SWError {
    constructor() {
        super(400,
            852,
            "Nickname must contain only English letters, numbers and keyboard special symbols (_!#$%&*, etc.)");
    }
}

export class NicknameMinSymbolsError extends SWError {
    constructor(length: number) {
        super(400, 853, `Nickname must contain a minimum of ${length} symbols`);
    }
}

export class NicknameMaxSymbolsError extends SWError {
    constructor(length: number) {
        super(400, 854, `Nickname cannot exceed ${length} symbols.`);
    }
}

export class NicknameBadWordsError extends SWError {
    constructor() {
        super(400, 855, "Nickname contains a word or phrase that is not allowed");
    }
}

export class NicknameChangeExceededError extends SWError {
    constructor() {
        super(400, 862, "Number of attempts to change nickname exceeded");
    }
}

export class MerchantTestPlayerError extends SWError {
    constructor(message: string) {
        super(400, 856, message);
    }
}

export class PlayerAlreadyHasSession extends SWError {
    constructor() {
        super(400, 860, "Player has another session");
    }
}

export class FlatReportAlreadyExists extends SWError {
    constructor() {
        super(409, 861, "Flat Report already exists");
    }
}

export class ChangeUserTypeToStudioUserError extends SWError {
    constructor() {
        super(400, 863, "Can't change user type to studio_user using this method");
    }
}

export class ChangeUserTypeWhenStudioUserTypeHasError extends SWError {
    constructor() {
        super(400, 864, "Can't change user type because user has studio_user type");
    }
}

export class UpdateNotLiveGameOfEntityError extends SWError {
    constructor() {
        super(400, 865, "Only live games can be updated using this method");
    }
}

export class UpdateLiveGameOfEntityError extends SWError {
    constructor() {
        super(400, 866, "Live games can't be updated using this method");
    }
}

export class ParentGameTypeNotMatchError extends SWError {
    constructor() {
        super(400, 867, "Parent game type not match with child game type");
    }
}

export class CreateReportDomainsError extends SWError {
    constructor(message?: string) {
        super(400, 868, message || "Create report domains error");
    }
}

export class DeleteReportDomainsError extends SWError {
    constructor(message?: string) {
        super(400, 869, message || "Delete report domains error");
    }
}

export class FunCurrencyPlayedNotInFunModeError extends SWError {
    constructor() {
        super(400, 870, "FUN currency can only be played in fun mode");
    }
}

export class ProhibitingCreationOfLiveGameError extends SWError {
    constructor() {
        super(400, 871, "Live games can be created through Live Admin(common service) only");
    }
}

// =====================================================================================================================
// ============================================ Responsible gaming errors ==============================================
// ======================================= codes must be in range of 1500 - 1600 =======================================
// =====================================================================================================================

export class PlayerOnTimeoutError extends SWError {
    constructor(public timeoutTill?: Date) {
        super(403, 1500, "Can't execute operation. Player is on timeout.");
    }

    public decorateResponseWithData(errorResponse: ErrorResponse): ErrorResponse {
        if (this.timeoutTill) {
            errorResponse["till"] = this.timeoutTill.toJSON();
        }
        return errorResponse;
    }
}

export class PlayerIsSelfExcludedError extends SWError {

    private selfExclusionTIll?: Date;

    constructor(message?: string) {
        super(403, 1501, message ? message : "Can't execute operation. Player is self-excluded.");
    }

    public decorateResponseWithData(errorResponse: ErrorResponse): ErrorResponse {
        if (this.selfExclusionTIll) {
            errorResponse["till"] = this.selfExclusionTIll.toJSON();
        }
        return errorResponse;
    }

    public setSelfExclusionDate(date: Date): PlayerIsSelfExcludedError {
        this.selfExclusionTIll = date;
        return this;
    }
}

export class RepeatingSelfExclusionError extends SWError {
    constructor() {
        super(403, 1506, "Can't execute operation. Player is permanently blocked.");
    }
}

export class ReportingRequestCanceledByTimeoutError extends SWError {
    constructor() {
        super(403, 907, "Your request took too long time, please change your request.");
    }
}

export class IntegrationTestsAPIError extends SWError {
    constructor(statusCode?: number, err?: SWError) {
        const status = statusCode || 400;
        const code = err && err.code || 905;
        const message = err && err.message || "Integration Tests API respond with error";
        super(status, code, message);
    }
}

export class IntegrationTestsAPITransientError extends SWError {
    constructor(statusCode?: number, err?: SWError) {
        const status = statusCode || 500;
        const code = err && err.code || 906;
        const message = err && err.message || "Integration Tests API transient error";
        super(status, code, message);
    }
}

export class GenericResetPasswordError extends SWError {
    constructor(additionalData?: any) {
        super(404, 999, "If an account with this email exists, a password reset link will be sent");
        if (additionalData) {
            this.extraData = additionalData;
        }
    }
}

export class GenericUserLoginError extends SWError {
    constructor() {
        super(400, 998, "Login Failed", ERROR_LEVEL.WARN, {
            traceId: measures.measureProvider.getTraceID()
        });
    }
}

export class RoundAlreadyClosedError extends SWError {
    constructor() {
        super(400, 599, "The round is already closed");
    }
}

export class RoundNotFoundError extends SWError {
    constructor() {
        super(404, 533, "Round not found");
    }
}
