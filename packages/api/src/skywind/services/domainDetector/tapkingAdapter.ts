import { HttpClient, HttpClientConfig, HttpClientError, createHttpClient } from "../../utils/httpClient";
import { DomainDetectorAdapter, MonitoredDomain } from "./types";
import config from "../../config";

enum AccessStatus {
    AVAILABLE = "AVAILABLE",
    BLOCKED = "BLOCKED",
    UNKNOWN = "UNKNOWN"
}

enum CountryCode {
    TR = "TR"
}

enum HttpMethod {
    GET = "GET",
    POST = "POST",
    HEAD = "HEAD"
}

enum HttpSchema {
    HTTP = "HTTP",
    HTTPS = "HTTPS"
}

enum BodyPredicate {
    EQUALS = "EQUALS",
    CONTAINS = "CONTAINS"
}

interface Region {
    countryCode: CountryCode;
}

interface CodeValidityRule {
    from: number;
    to: number;
}

interface BodyValidityRule {
    predicate: BodyPredicate;
    content: string;
    ignoreCase?: boolean;
}

interface ValidityRule {
    code: CodeValidityRule;
    body?: BodyValidityRule | null;
}

interface CheckEndpoint {
    uri?: string | null;
    schema?: HttpSchema;
    headers?: Record<string, string[]>;
    method?: HttpMethod;
    body?: string | null;
    validityRule?: ValidityRule;
}

interface DomainStatus {
    region: Region;
    accessStatus: AccessStatus;
    lastCheckedAt?: string | null;
    checkId?: string | null;
}

interface TapkingMonitoredDomain {
    clientId?: string;
    name: string;
    createdAt?: number;
    checkEndpoint: CheckEndpoint;
    statuses: DomainStatus[];
}

function mapTo({ name, statuses }: TapkingMonitoredDomain): MonitoredDomain {
    return {
        domain: name,
        statuses: statuses.map(({ region, accessStatus, lastCheckedAt }) => ({
            region: region.countryCode,
            accessStatus,
            lastCheckedAt
        }))
    };
}

export class TapkingAdapter implements DomainDetectorAdapter {
    private readonly httpClient: HttpClient;

    constructor({ token: bearerToken, baseUrl, ...config }: HttpClientConfig & { token: string }) {
        this.httpClient = createHttpClient({
            ...config,
            baseUrl: `${baseUrl?.replace(/\/$/, "") ?? ""}/api/v1/domains`,
            headers: {
                "Authorization": `Bearer ${bearerToken}`,
                "Content-Type": "application/json"
            },
            wrapError: (err) => {
                if (err instanceof HttpClientError) {
                    throw new DomainValidatorError(err.message, err.statusCode, err.responseBody);
                }
                throw err;
            }
        });
    }

    public async register(domain: string) {
        const data = await this.httpClient.post<TapkingMonitoredDomain>("", { domain });
        return mapTo(data);
    }

    public async list() {
        const data = await this.httpClient.get<TapkingMonitoredDomain[]>("");
        return data.map(mapTo);
    }

    public async get(domain: string) {
        const data = await this.httpClient.get<TapkingMonitoredDomain>(`/${encodeURIComponent(domain)}`);
        return mapTo(data);
    }

    public async remove(domain: string) {
        await this.httpClient.delete(`/${encodeURIComponent(domain)}`);
    }
}

export class DomainValidatorError extends Error {
    public readonly statusCode?: number;
    public readonly responseBody?: any;

    constructor(message: string, statusCode?: number, responseBody?: any) {
        super(message);
        this.name = "DomainValidatorError";
        this.statusCode = statusCode;
        this.responseBody = responseBody;
    }
}

export function getTapkingAdapter() {
    return new TapkingAdapter({
        baseUrl: config.domainDetector.tapking.baseUrl,
        token: config.domainDetector.tapking.token,
        timeout: config.domainDetector.timeout,
        retryConfig: {
            sleep: config.domainDetector.retryDelay,
            maxTimeout: config.domainDetector.retryAttempts * config.domainDetector.retryDelay
        },
        keepAlive: config.domainDetector.keepAlive
    });
}
