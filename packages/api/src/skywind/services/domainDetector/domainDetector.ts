import { getTapkingAdapter } from "./tapkingAdapter";
import { DomainDetectorAdapter } from "./types";

export function getAdapter(adapter: string): DomainDetectorAdapter {
    if (adapter === "tapking") {
        return getTapkingAdapter();
    }
    throw new Error(`Domain detector adapter ${adapter} is not supported.`);
}

class DomainDetector {

    constructor(private readonly adapter: DomainDetectorAdapter) {
    }
}

export function getDomainDetector(adapter: string): DomainDetector {
    return new DomainDetector(getAdapter(adapter));
}
