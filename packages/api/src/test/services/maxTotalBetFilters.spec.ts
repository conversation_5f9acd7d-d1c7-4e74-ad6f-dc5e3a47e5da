import { suite, test } from "mocha-typescript";
import { expect, should, use } from "chai";
import * as chaiAsPromised from "chai-as-promised";
import { truncate } from "../entities/helper";
import { factory } from "factory-girl";
import { FACTORY } from "../factories/common";
import { EntityGame } from "../../skywind/entities/game";
import { findPlayerLimits } from "../../skywind/services/limits";
import { SinonFakeTimers, SinonStub, stub, useFakeTimers } from "sinon";
import * as gameLimitsCurrenciesCache from "../../skywind/cache/gameLimitsCurrencies";
import * as EntityJurisdictionCache from "../../skywind/cache/entityJurisdiction";

should();
use(chaiAsPromised);

@suite()
class MaxTotalBetFiltersSpec {
    public static entityGame: EntityGame;
    public clock: SinonFakeTimers;
    public static gameLimitsCurrenciesCacheStub: SinonStub;

    public static async before() {
        await truncate();
        await gameLimitsCurrenciesCache.reset();
        MaxTotalBetFiltersSpec.gameLimitsCurrenciesCacheStub = stub(gameLimitsCurrenciesCache, "getGameLimitsCurrency");
        MaxTotalBetFiltersSpec.gameLimitsCurrenciesCacheStub.resolves({});
    }

    public static after() {
        MaxTotalBetFiltersSpec.gameLimitsCurrenciesCacheStub.restore();
    }

    public before() {
        this.clock = useFakeTimers();
        this.clock.setSystemTime(0);
    }

    public after() {
        this.clock.restore();
    }

    @test()
    public async testMaxTotalStakeIsLimitedByOperatorAndFeatureIsNotLimited() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [
                            1, 2, 3, 5
                        ],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            maxTotalBet: 25,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: false } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 100,
            stakeAll: [1, 2],
            stakeDef: 2,
            stakeMax: 2,
            stakeMin: 1,
            winMax: 500000
        });
    }

    @test()
    public async testMaxTotalStakeIsLimitedByOperatorAndFeatureIsLimitedByOperator() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [
                            1, 2, 3, 5
                        ],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            maxTotalBet: 25,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: true } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 25,
            stakeAll: [1, 2],
            stakeDef: 2,
            stakeMax: 2,
            stakeMin: 1,
            winMax: 500000
        });
    }

    @test()
    public async testMaxTotalStakeAndFeaturesIsLimitedByRegulation() {
        EntityJurisdictionCache.reset();
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [
                            1, 2, 3, 5
                        ],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    maxTotalStake: 25
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            undefined,
            { limitFeaturesToMaxTotalStake: false } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 25,
            stakeAll: [1, 2],
            stakeDef: 2,
            stakeMax: 2,
            stakeMin: 1,
            winMax: 500000
        });
    }

    @test()
    public async testMaxTotalStakeIsLimitedByOperatorAndRegulationWhereOperatorMTBMoreThanRegulation() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [
                            1, 2, 3, 5
                        ],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            maxTotalBet: 45,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    maxTotalStake: 30
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.get("name"),
            { limitFeaturesToMaxTotalStake: false } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 30,
            stakeAll: [1, 2, 3],
            stakeDef: 2,
            stakeMax: 3,
            stakeMin: 1,
            winMax: 500000
        });
    }

    @test()
    public async testMaxTotalStakeIsLimitedByOperatorAndRegulationWhereRegulationMTBMoreThanOperator() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [
                            1, 2, 3, 5
                        ],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            maxTotalBet: 25,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    maxTotalStake: 45
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.get("name"),
            { limitFeaturesToMaxTotalStake: false } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 45,
            stakeAll: [1, 2],
            stakeDef: 2,
            stakeMax: 2,
            stakeMin: 1,
            winMax: 500000
        });
    }

    @test()
    public async testMaxTotalStakeAndFeatureIsLimitedByRegulationAndOperatorWhereOperatorMTBMoreThanRegulation() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [
                            1, 2, 3, 5
                        ],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            maxTotalBet: 45,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    maxTotalStake: 30
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.get("name"),
            { limitFeaturesToMaxTotalStake: true } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 30,
            stakeAll: [1, 2, 3],
            stakeDef: 2,
            stakeMax: 3,
            stakeMin: 1,
            winMax: 500000
        });
    }

    @test()
    public async testMaxTotalStakeAndFeatureIsLimitedByRegulationAndOperatorWhereRegulationMTBMoreThanOperator() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [
                            1, 2, 3, 5
                        ],
                        stakeDef: 2,
                        stakeMax: 5,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        await factory.create(FACTORY.GAME_GROUP_FILTER, {}, {
            currencies: [],
            games: [],
            maxTotalBet: 30,
            groupId: gameGroup.get("id")
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    maxTotalStake: 45
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.get("name"),
            { limitFeaturesToMaxTotalStake: true } as any);
        expect(result).to.be.deep.equal({
            maxTotalStake: 30,
            stakeAll: [1, 2, 3],
            stakeDef: 2,
            stakeMax: 3,
            stakeMin: 1,
            winMax: 500000
        });
    }

    @test()
    public async testDynamicDefaultTotalBetSetsStakeDef() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [1, 2, 3, 5, 10],
                        stakeDef: 2,
                        stakeMax: 10,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    dynamicMaxTotalBetLimitEnabled: true,
                    maxTotalStake: 80
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: false } as any,
            false,
            undefined,
            50, // dynamicDefaultTotalBet
            undefined);
        
        expect(result).to.be.deep.equal({
            maxTotalStake: 80,
            stakeAll: [1, 2, 3, 5],
            stakeDef: 5, // Should be set to dynamicDefaultTotalBet / totalBetMultiplier = 50 / 10 = 5
            stakeMax: 5,
            stakeMin: 1,
            winMax: 500000
        });
    }

    @test()
    public async testDynamicMinTotalBetLimitFiltersStakeAll() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [1, 2, 3, 5, 10],
                        stakeDef: 2,
                        stakeMax: 10,
                        stakeMin: 1,
                        maxTotalStake: 100
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    dynamicMaxTotalBetLimitEnabled: true,
                    maxTotalStake: 80
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: false } as any,
            false,
            undefined,
            undefined,
            30); // dynamicMinTotalBetLimit
        
        expect(result).to.be.deep.equal({
            maxTotalStake: 80,
            stakeAll: [3, 5], // Filtered to stakes >= 30/10 = 3
            stakeDef: 3, // Falls back to minimum available stake since original stakeDef (2) is filtered out
            stakeMax: 5,
            stakeMin: 1,
            winMax: 500000
        });
    }

    @test()
    public async testDynamicLimitsCombined() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [1, 2, 3, 5, 8, 10],
                        stakeDef: 2,
                        stakeMax: 10,
                        stakeMin: 1,
                        maxTotalStake: 150
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    dynamicMaxTotalBetLimitEnabled: true,
                    maxTotalStake: 100
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: false } as any,
            false,
            80, // dynamicMaxTotalBetLimit
            50, // dynamicDefaultTotalBet
            30); // dynamicMinTotalBetLimit
        
        expect(result).to.be.deep.equal({
            maxTotalStake: 80, // Limited by dynamicMaxTotalBetLimit
            stakeAll: [3, 5, 8], // Filtered: >= 30/10 = 3 AND <= 80/10 = 8, available: [3, 5, 8]
            stakeDef: 5, // Set to dynamicDefaultTotalBet / totalBetMultiplier = 50 / 10 = 5
            stakeMax: 8,
            stakeMin: 1,
            winMax: 500000
        });
    }

    @test()
    public async testDynamicDefaultTotalBetFallbackWhenNotInFilteredStakes() {
        const parentEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                totalBetMultiplier: 10,
                features: { decreaseMaxBetSupported: true, increaseMinBetSupported: true, highestWin: 1000 },
                limits: {
                    EUR: {
                        winMax: 500000,
                        stakeAll: [1, 2, 3, 5, 8, 10],
                        stakeDef: 2,
                        stakeMax: 10,
                        stakeMin: 1,
                        maxTotalStake: 150
                    }
                }
            }
        });
        const brand = await factory.create(FACTORY.BRAND);
        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: brand.id
        });

        const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: parentEntityGame.gameId,
            parentEntityGameId: parentEntityGame.id,
            entityId: brand.id
        });

        await factory.create(FACTORY.ENTITY_JURISDICTION, {}, {
            entityId: brand.id,
            jurisdictionBuildOptions: {
                settings: {
                    dynamicMaxTotalBetLimitEnabled: true,
                    maxTotalStake: 100
                }
            }
        });

        const [result] = await findPlayerLimits(brand,
            entityGame,
            "EUR",
            gameGroup.name,
            { limitFeaturesToMaxTotalStake: false } as any,
            false,
            60, // dynamicMaxTotalBetLimit
            70, // dynamicDefaultTotalBet - not available in filtered stakes
            35); // dynamicMinTotalBetLimit
        
        expect(result).to.be.deep.equal({
            maxTotalStake: 60, // Limited by dynamicMaxTotalBetLimit
            stakeAll: [5], // Only stake that meets both min >= 35/10=3.5 (rounded up to 5) and max <= 60/10=6
            stakeDef: 5, // Falls back to minimum available since 70/10=7 is not in filtered stakes
            stakeMax: 5,
            stakeMin: 1,
            winMax: 500000
        });
    }

}
